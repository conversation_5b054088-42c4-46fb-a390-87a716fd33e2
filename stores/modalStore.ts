// stores/modalStore.ts
import { create } from 'zustand';

// 定义所有可用 Modal 的唯一标识符
export type ModalType = 'login' | 'pricing-modal' | 'feedback' | 'aiModel' | 'userProfile' | 'settings' | 'my-orders' | 'my-credits' | 'my-invites' | 'api-keys';

// 为每个 Modal 定义它可能接收的 props 类型
// 这为类型安全和智能提示提供了保障
interface ModalProps {
  login: {}; // 登录 Modal 通常不需要 props
  'pricing-modal': {
    locale?: string; // 语言环境
    source?: string; // 来源页面，用于分析
  };
  feedback: {
    socialLinks?: Array<{
      name: string;
      url: string;
      icon?: string;
    }>;
  };
  aiModel: {
    modelType?: 'text' | 'image' | 'video' | 'audio';
    modelId?: string;
  };
  userProfile: {
    userId?: string;
    tab?: 'profile' | 'settings' | 'billing';
  };
  settings: {
    section?: 'general' | 'appearance' | 'notifications' | 'privacy';
  };
  'my-orders': {
    locale?: string;
  };
  'my-credits': {
    locale?: string;
  };
  'my-invites': {
    locale?: string;
  };
  'api-keys': {
    locale?: string;
  };
}

// 定义 Store 的 state 结构和 actions
interface ModalStore {
  activeModal: {
    type: ModalType;
    props: ModalProps[ModalType]; // props 类型与 type 联动，非常安全
  } | null;
  openModal: <T extends ModalType>(type: T, props: ModalProps[T]) => void;
  closeModal: () => void;
  isModalOpen: (type?: ModalType) => boolean;
}

// 创建 Zustand store
export const useModalStore = create<ModalStore>((set, get) => ({
  // 初始状态下，没有激活的 Modal
  activeModal: null,

  // 打开一个 Modal 的 action，接收类型和 props
  openModal: (type, props) => set({ activeModal: { type, props } }),

  // 关闭当前激活的 Modal 的 action
  closeModal: () => set({ activeModal: null }),

  // 检查是否有模态框打开，可选择检查特定类型
  isModalOpen: (type) => {
    const { activeModal } = get();
    if (!activeModal) return false;
    return type ? activeModal.type === type : true;
  },
}));
