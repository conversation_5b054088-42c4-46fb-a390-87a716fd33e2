import { getSupabaseClient } from "@/models/db";
import { auth } from "@/auth";
import { User } from "@/types/user";
import { findUserByEmail, insertUser } from "@/models/user";

// 用户订阅访问权限检查
export async function checkUserSubscriptionAccess(userUuid: string): Promise<{
  hasAccess: boolean;
  accessType: 'active' | 'trial' | 'grace_period' | 'expired';
  expiresAt?: string;
  subscriptionStatus?: string;
}> {
  try {
    const supabase = getSupabaseClient();
    
    // 查找用户的订阅订单
    const { data: orders, error } = await supabase
      .from("orders")
      .select("*")
      .eq("user_uuid", userUuid)
      .not("sub_id", "is", null)
      .in("sub_status", ["active", "trialing", "canceled", "expired"])
      .order("created_at", { ascending: false });

    if (error || !orders || orders.length === 0) {
      return { hasAccess: false, accessType: 'expired' };
    }

    const latestOrder = orders[0];
    const now = new Date();
    const periodEnd = new Date(latestOrder.sub_period_end * 1000);

    switch (latestOrder.sub_status) {
      case 'active':
        return {
          hasAccess: true,
          accessType: 'active',
          expiresAt: periodEnd.toISOString(),
          subscriptionStatus: 'active'
        };

      case 'trialing':
        return {
          hasAccess: true,
          accessType: 'trial',
          expiresAt: periodEnd.toISOString(),
          subscriptionStatus: 'trialing'
        };

      case 'canceled':
        // 取消的订阅可以使用到周期结束
        if (now < periodEnd) {
          return {
            hasAccess: true,
            accessType: 'grace_period',
            expiresAt: periodEnd.toISOString(),
            subscriptionStatus: 'canceled'
          };
        } else {
          return {
            hasAccess: false,
            accessType: 'expired',
            subscriptionStatus: 'canceled'
          };
        }

      case 'expired':
        // 付款失败状态，可能会重试，在周期内仍可使用
        if (now < periodEnd) {
          return {
            hasAccess: true,
            accessType: 'grace_period', // 宽限期，等待付款重试
            expiresAt: periodEnd.toISOString(),
            subscriptionStatus: 'expired'
          };
        } else {
          return {
            hasAccess: false,
            accessType: 'expired',
            subscriptionStatus: 'expired'
          };
        }

      default:
        return { hasAccess: false, accessType: 'expired' };
    }
  } catch (error) {
    console.error('Failed to check user subscription access:', error);
    return { hasAccess: false, accessType: 'expired' };
  }
}

// 获取用户的活跃订阅信息
export async function getUserActiveSubscription(userUuid: string) {
  try {
    const supabase = getSupabaseClient();
    
    const { data: order, error } = await supabase
      .from("orders")
      .select("*")
      .eq("user_uuid", userUuid)
      .not("sub_id", "is", null)
      .in("sub_status", ["active", "trialing", "canceled", "expired"])
      .order("created_at", { ascending: false })
      .limit(1)
      .single();

    if (error || !order) {
      return null;
    }

    return {
      subscriptionId: order.sub_id,
      status: order.sub_status,
      productId: order.product_id,
      credits: order.credits,
      periodStart: new Date(order.sub_period_start * 1000).toISOString(),
      periodEnd: new Date(order.sub_period_end * 1000).toISOString(),
      canceledAt: order.sub_canceled_at,
      orderNo: order.order_no
    };
  } catch (error) {
    console.error('Failed to get user active subscription:', error);
    return null;
  }
}

// 获取当前登录用户的 UUID
export async function getUserUuid(): Promise<string | null> {
  try {
    // console.log('[getUserUuid] Calling auth()...');
    const session = await auth();
    // console.log('[getUserUuid] Session result:', session);
    
    if (!session || !session.user || !session.user.uuid) {
      // console.log('[getUserUuid] No valid session or user UUID, returning null');
      return null;
    }
    
    // console.log('[getUserUuid] Returning user UUID:', session.user.uuid);
    return session.user.uuid;
  } catch (error) {
    // console.error('[getUserUuid] Failed to get user UUID:', error);
    return null;
  }
}

// 获取当前登录用户的邮箱
export async function getUserEmail(): Promise<string | null> {
  try {
    const session = await auth();
    if (!session || !session.user || !session.user.email) {
      return null;
    }
    return session.user.email;
  } catch (error) {
    console.error('Failed to get user email:', error);
    return null;
  }
}

// 保存或更新用户信息
export async function saveUser(user: User): Promise<User> {
  try {
    // 首先尝试根据邮箱查找现有用户
    const existingUser = await findUserByEmail(user.email);
    
    if (existingUser) {
      // 用户已存在，返回现有用户（简化处理）
      return existingUser;
    } else {
      // 用户不存在，创建新用户
      const newUser = await insertUser(user);
      return newUser;
    }
  } catch (error) {
    console.error('Failed to save user:', error);
    throw error;
  }
}