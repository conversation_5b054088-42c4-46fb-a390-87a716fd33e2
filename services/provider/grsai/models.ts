/**
 * GRSAI 模型配置聚合和导出
 */

import { GRSAI_TEXT_MODELS } from './text-models';
import { GRSAI_MEDIA_MODELS } from './media-models';
import { AIModelConfig } from '../types/model';
import { ModelParameterConfig } from '../types';

/**
 * 所有 GRSAI 模型配置
 */
export const ALL_GRSAI_MODELS: AIModelConfig[] = [
  ...GRSAI_TEXT_MODELS,
  ...GRSAI_MEDIA_MODELS
];

/**
 * 转换为参数管理器兼容的格式（保持向后兼容）
 */
export const ALL_GRSAI_PARAMETER_CONFIGS: ModelParameterConfig[] = (() => {
  try {
    if (!Array.isArray(ALL_GRSAI_MODELS)) {
      console.error('[GRSAI Models] ALL_GRSAI_MODELS is not an array:', ALL_GRSAI_MODELS);
      return [];
    }
    
    return ALL_GRSAI_MODELS.map(model => {
      if (!model || typeof model !== 'object') {
        console.error('[GRSAI Models] Invalid model:', model);
        return null;
      }
      
      return {
        modelId: model.model_id,
        version: '1.0',
        provider: model.provider,
        modelType: model.model_type,
        parameters: model.parameters || [],
        parameterGroups: model.parameter_groups || { basic: [], advanced: [], expert: [] }
      };
    }).filter(Boolean) as ModelParameterConfig[];
  } catch (error) {
    console.error('[GRSAI Models] Error creating parameter configs:', error);
    return [];
  }
})();

/**
 * 根据模型ID获取模型配置
 */
export function getModelConfig(modelId: string): AIModelConfig | null {
  return ALL_GRSAI_MODELS.find(model => model.id === modelId) || null;
}

/**
 * 根据模型类型获取模型配置
 */
export function getModelsByType(type: string): AIModelConfig[] {
  return ALL_GRSAI_MODELS.filter(model => model.model_type === type && model.is_active);
}

/**
 * 获取所有活跃的模型
 */
export function getActiveModels(): AIModelConfig[] {
  return ALL_GRSAI_MODELS.filter(model => model.is_active);
}

/**
 * 根据模型ID获取参数配置
 */
export function getParameterConfigByModelId(modelId: string): ModelParameterConfig | null {
  return ALL_GRSAI_PARAMETER_CONFIGS.find(config => config.modelId === modelId) || null;
}

/**
 * 获取模型的默认参数值
 */
export function getModelDefaultParameters(modelId: string): Record<string, any> {
  const config = getParameterConfigByModelId(modelId);
  if (!config) return {};

  const defaults: Record<string, any> = {};
  config.parameters.forEach(param => {
    if (param.default !== undefined) {
      defaults[param.name] = param.default;
    }
  });

  return defaults;
}

// 为了向后兼容，保留旧的导出名称
export const ALL_GRSAI_UNIFIED_MODELS = ALL_GRSAI_MODELS;
export const getUnifiedModelConfig = getModelConfig;
export const getUnifiedModelsByType = getModelsByType;
export const getActiveUnifiedModels = getActiveModels;