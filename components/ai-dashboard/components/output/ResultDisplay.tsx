"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import { toast } from "sonner";
import {
  Brain,
  CheckCircle,
  AlertCircle,
  Loader2,
  Eye,
  Download,
  Coins
} from "lucide-react";
import { useTranslations } from "next-intl";
import { ImagePreviewModal } from "./ImagePreviewModal";
import { downloadFileFromUrl, getFileExtensionFromUrl } from "@/lib/download-utils";
import { getImageFromCache, setImageToCache } from "@/lib/image-cache";
import { useAIGeneration } from "../hooks/useAIGeneration";
import type { WorkspaceGenerationResult } from "../types";

export function ResultDisplay() {
  const t = useTranslations("ai-dashboard");
  const { state } = useAIGeneration();
  
  // 直接使用Context中的数据
  const { generationResult, formData, selectedModel, generationHistory, loading } = state;
  const prompt = formData.prompt;
  
  // 生成文件名的辅助函数
  const generateFileName = (url: string, index?: number, type: 'image' | 'video' = 'image') => {
    const webUrl = process.env.NEXT_PUBLIC_WEB_URL || 'localhost';
    const date = new Date().toISOString().split('T')[0]; // YYYY-MM-DD 格式
    const extension = getFileExtensionFromUrl(url);

    // 清理提示语，移除特殊字符并限制长度
    const cleanPrompt = prompt
      ? prompt.replace(/[^\w\s-]/g, '').replace(/\s+/g, '-').substring(0, 50)
      : '';

    // 构建文件名：网站域名_日期_提示语_序号.扩展名
    const parts = [
      webUrl.replace(/https?:\/\//, '').replace(/[^\w-]/g, '-'),
      date,
      cleanPrompt,
      type === 'image' && index !== undefined ? `${index + 1}` : ''
    ].filter(Boolean);

    return `${parts.join('_')}.${extension}`;
  };

  // 使用实际的生成结果
  const displayResult = generationResult;  

  // 图片预览状态
  const [previewImage, setPreviewImage] = useState<{
    url: string;
    alt: string;
  } | null>(null);

  // 下载状态
  const [downloadingImages, setDownloadingImages] = useState<Set<string>>(new Set());
  const [downloadingVideo, setDownloadingVideo] = useState(false);

  // 处理图片预览
  const handleImagePreview = (imageUrl: string, imageAlt: string) => {
    setPreviewImage({ url: imageUrl, alt: imageAlt });
  };

  // 图片加载后自动缓存
  const cacheImageOnLoad = async (imageUrl: string) => {
    try {
      // 检查是否已在缓存中，避免重复操作
      const cachedBlob = await getImageFromCache(imageUrl);
      if (cachedBlob) {
        return;
      }

      // 从网络获取图片并存入缓存
      // 由于图片刚刚被 <img> 标签加载，此 fetch 很可能会命中浏览器缓存
      const response = await fetch(imageUrl,
        {
          method: 'GET', // 可以是 GET, POST, PUT, DELETE 等
          headers: {
            "Access-Control-Allow-Origin": process.env.NEXT_PUBLIC_WEB_URL || "http://localhost:3000",
          }
        }

      );
      if (!response.ok) {
        throw new Error(`cacheImageOnLoad: Failed to fetch image for caching: ${response.status}`);
      }
      const blob = await response.blob();
      await setImageToCache(imageUrl, blob);
    } catch (error) {
      // 缓存失败是一个非关键错误，只需记录，不打断用户
      console.error('Failed to cache image on load:', error);
    }
  };

  // 处理图片下载
  const handleImageDownload = async (imageUrl: string, index?: number) => {
    const fileName = generateFileName(imageUrl, index, 'image');

    // 1. 首先检查缓存
    const cachedBlob = await getImageFromCache(imageUrl);

    if (cachedBlob) {
      // 从缓存直接下载，无需显示加载状态
      const blobUrl = URL.createObjectURL(cachedBlob);
      const link = document.createElement('a');
      link.href = blobUrl;
      link.download = fileName;
      link.style.display = 'none';

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      URL.revokeObjectURL(blobUrl);
      // toast.success('图片下载成功（来自缓存）');
      return;
    }

    // 2. 通过 fetch 获取图片数据并下载（Blob 方式）
    setDownloadingImages(prev => new Set(prev).add(imageUrl));

    try {
      console.log('[Download] Fetching image data:', imageUrl);

      const response = await fetch(imageUrl);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const blob = await response.blob();

      // 存储到缓存
      await setImageToCache(imageUrl, blob);

      // 创建 Blob URL 并下载
      const blobUrl = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = blobUrl;
      link.download = fileName;
      link.style.display = 'none';

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // 清理 Blob URL
      URL.revokeObjectURL(blobUrl);
      toast.success(t("actions.download_success"));

    } catch (error) {
      console.error('Download error:', error);
      toast.error(t("actions.download_failed"));
    } finally {
      setDownloadingImages(prev => {
        const newSet = new Set(prev);
        newSet.delete(imageUrl);
        return newSet;
      });
    }
  };

  // 处理视频下载
  const handleVideoDownload = async (videoUrl: string) => {
    setDownloadingVideo(true);

    try {
      const fileName = generateFileName(videoUrl, undefined, 'video');

      // 直接下载文件，R2已配置CORS
      const result = await downloadFileFromUrl(videoUrl, fileName);

      if (result.success) {
        toast.success('视频下载成功');
      } else {
        toast.error(`下载失败: ${result.error}`);
      }
    } catch (error) {
      toast.error('下载失败，请重试');
      console.log(error)
    } finally {
      setDownloadingVideo(false);
    }
  };

  // 如果正在加载且没有结果，显示生成中状态
  if (loading && !displayResult) {
    return (
      <div className="text-center py-12">
        <div className="p-6 rounded-2xl bg-gradient-to-br from-blue-50/50 to-indigo-50/50 dark:from-blue-950/20 dark:to-indigo-950/20 border border-blue-200/30 dark:border-blue-800/30 max-w-md mx-auto">
          <div className="p-4 rounded-xl bg-gradient-to-r from-blue-500 to-purple-500 mb-4 w-fit mx-auto">
            <Loader2 className="w-8 h-8 text-white animate-spin" />
          </div>
          <h3 className="text-lg font-semibold text-foreground mb-2">{t("status.pending")}</h3>
          <p className="text-muted-foreground text-sm">
            请稍候，AI正在为您创作内容
          </p>
        </div>
      </div>
    );
  }

  // 如果没有结果且不在加载中，显示空状态
  if (!displayResult) {
    return (
      <div className="text-center py-12">
        <div className="p-6 rounded-2xl bg-gradient-to-br from-muted/30 to-muted/10 border border-border/30 max-w-md mx-auto">
          <div className="p-4 rounded-xl bg-gradient-to-r from-primary to-accent mb-4 w-fit mx-auto">
            <Brain className="w-8 h-8 text-primary-foreground" />
          </div>
          <h3 className="text-lg font-semibold text-foreground mb-2">{t("workspace.start_create")}</h3>
          <p className="text-muted-foreground text-sm">
            {t("workspace.choose_model")}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="">
      {/* 显示当前配置信息 */}
      {/* {selectedModel && formData && (
        <div className="mb-4 p-4 bg-gradient-to-br from-blue-50/50 to-indigo-50/50 dark:from-blue-950/20 dark:to-indigo-950/20 rounded-xl border border-blue-200/30 dark:border-blue-800/30">
          <h3 className="text-sm font-semibold text-blue-900 dark:text-blue-100 mb-2">当前配置</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-xs text-blue-800 dark:text-blue-200">
            <div><span className="font-medium">模型:</span> {selectedModel.model_name}</div>
            <div><span className="font-medium">类型:</span> {formData.modelType}</div>
            <div className="md:col-span-2"><span className="font-medium">提示词:</span> {formData.prompt.substring(0, 100)}{formData.prompt.length > 100 ? '...' : ''}</div>
            {formData.options?.variants && formData.options.variants > 1 && (
              <div><span className="font-medium">变体数量:</span> {formData.options.variants}</div>
            )}
            {formData.options?.size && (
              <div><span className="font-medium">尺寸:</span> {formData.options.size}</div>
            )}
          </div>
        </div>
      )} */}

      {/* 生成状态 */}
      <div className="flex items-center  rounded-xl bg-gradient-to-r from-muted/30 to-muted/10 border border-border/30">
        <div className={`p-2 rounded-lg ${displayResult.status === 'success' ? 'hidden' :
          displayResult.status === 'failed' ? 'bg-gradient-to-r from-red-500 to-rose-500' :
            'bg-gradient-to-r from-blue-500 to-purple-500'
          }`}>
          {/* {displayResult.status === 'success' && <CheckCircle className="w-5 h-5 text-white" />} */}
          {displayResult.status === 'failed' && <AlertCircle className="w-5 h-5 text-white" />}
          {(displayResult.status === 'pending' || displayResult.status === 'running') && <Loader2 className="w-5 h-5 animate-spin text-white" />}
        </div>
        <div className=" w-full">
          <span className="font-semibold text-foreground">
            {/* {displayResult.status === 'success' && '生成完成'} */}
            {displayResult.status === 'failed' && t("status.failed")}
            {displayResult.status === 'pending' && t("status.pending")}
            {displayResult.status === 'running' && t("status.running")}
          </span>
          {(displayResult.progress !== undefined && displayResult.status !== 'success') && (
            <div className="space-y-2">
              <div className="text-sm text-muted-foreground">{t("status.progress")}: {displayResult.progress}%</div>
              <Progress value={displayResult.progress} className="w-full" />
            </div>
          )}
        </div>
      </div>

      {/* 结果内容 */}
      {displayResult.status === 'success' && displayResult.result && (
        <div className="space-y-4">
          {/* 文本结果 */}
          {displayResult.result.text && (
            <div className="p-6 bg-gradient-to-br from-muted/20 to-muted/10 rounded-xl border border-border/30 backdrop-blur-sm">
              <pre className="whitespace-pre-wrap text-sm leading-relaxed text-foreground">{displayResult.result.text}</pre>
            </div>
          )}

          {/* 图像结果 */}
          {displayResult.result.images && (
            <div className="grid grid-cols-1 gap-4">
              {displayResult.result.images.map((image, index) => {
                const isDownloading = downloadingImages.has(image.url);
                const imageAlt = `Generated image ${index + 1}`;

                return (
                  <div key={index} className="space-y-4 p-4 bg-gradient-to-br from-muted/20 to-muted/10 rounded-xl border border-border/30">
                    <div
                      className="relative cursor-pointer group"
                      onClick={() => handleImagePreview(image.url, imageAlt)}
                    >
                      <img
                        src={image.url}
                        alt={imageAlt}
                        className="w-full rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 group-hover:scale-[1.02]"
                        onLoad={() => cacheImageOnLoad(image.url)}
                      />
                      {/* 预览提示覆盖层 */}
                      <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-200 rounded-xl flex items-center justify-center">
                        <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 bg-white/90 rounded-full p-3">
                          <Eye className="w-6 h-6 text-gray-800" />
                        </div>
                      </div>
                    </div>
                    <div className="flex gap-3">
                      <Button
                        size="sm"
                        variant="outline"
                        className="flex-1"
                        onClick={() => handleImagePreview(image.url, imageAlt)}
                      >
                        <Eye className="w-4 h-4 mr-2" />
                        {t("actions.view")}
                      </Button>
                      <Button
                        size="sm"
                        variant="default"
                        className="flex-1"
                        onClick={() => handleImageDownload(image.url, index)}
                        disabled={isDownloading}
                      >
                        {isDownloading ? (
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        ) : (
                          <Download className="w-4 h-4 mr-2" />
                        )}
                        {isDownloading ? t("actions.downloading") : t("actions.download")}
                      </Button>
                    </div>
                  </div>
                );
              })}
            </div>
          )}

          {/* 视频结果 */}
          {displayResult.result.video && (
            <div className="space-y-4 p-4 bg-gradient-to-br from-muted/20 to-muted/10 rounded-xl border border-border/30">
              <video
                src={displayResult.result.video.url}
                controls
                className="w-full rounded-xl shadow-lg"
              />
              <div className="flex gap-3">
                <Button
                  size="sm"
                  variant="outline"
                  className="flex-1"
                  onClick={() => window.open(displayResult.result?.video?.url, '_blank')}
                >
                  <Eye className="w-4 h-4 mr-2" />
                  在新窗口查看
                </Button>
                <Button
                  size="sm"
                  variant="default"
                  className="flex-1"
                  onClick={() => displayResult.result?.video?.url && handleVideoDownload(displayResult.result.video.url)}
                  disabled={downloadingVideo}
                >
                  {downloadingVideo ? (
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  ) : (
                    <Download className="w-4 h-4 mr-2" />
                  )}
                  {downloadingVideo ? t("actions.downloading") : t("actions.download")}
                </Button>
              </div>
            </div>
          )}

          {/* 使用统计 */}
          {displayResult.usage && (
            <div className="flex items-center gap-3 p-3 bg-gradient-to-r from-yellow-500/10 to-orange-500/10 rounded-lg border border-yellow-500/20">
              <div className="p-2 rounded-lg bg-gradient-to-r from-yellow-500 to-orange-500">
                <Coins className="w-4 h-4 text-white" />
              </div>
              <span className="text-sm font-medium text-foreground">
                {t("cost.consumed", { amount: displayResult.usage.credits_consumed })}
              </span>
            </div>
          )}
        </div>
      )}

      {/* 错误信息 */}
      {displayResult.status === 'failed' && displayResult.error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {t("errors.generation_failed", { detail: displayResult.error.detail })}
          </AlertDescription>
        </Alert>
      )}

      {/* 进度显示 */}
      {(displayResult.status === 'pending' || displayResult.status === 'running') && (
        <div className="text-center py-4">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-2" />
          <p className="text-sm text-gray-600">
            Generating... please wait...
            {displayResult.progress !== undefined && ` (${displayResult.progress}%)`}
          </p>
        </div>
      )}

      {/* 历史记录 */}
      {generationHistory.length > 0 && (
        <div className="mt-6 p-4 bg-gradient-to-br from-gray-50/50 to-slate-50/50 dark:from-gray-950/20 dark:to-slate-950/20 rounded-xl border border-gray-200/30 dark:border-gray-800/30">
          <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-3">生成历史</h3>
          <div className="space-y-3 max-h-96 overflow-y-auto">
            {generationHistory.map((item, index) => (
              <div key={index} className="p-3 bg-white/50 dark:bg-gray-900/50 rounded-lg border border-gray-200/50 dark:border-gray-700/50">
                <div className="flex items-start justify-between mb-2">
                  <div className="flex-1">
                    <div className="text-xs text-gray-600 dark:text-gray-400 mb-1">
                      {item.selectedModel?.model_name} • {item.timestamp?.toLocaleString()}
                    </div>
                    <div className="text-sm text-gray-800 dark:text-gray-200 line-clamp-2">
                      {item.formData?.prompt}
                    </div>
                  </div>
                  {item.creditsUsed && (
                    <div className="ml-2 px-2 py-1 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200 text-xs rounded">
                      {item.creditsUsed} 积分
                    </div>
                  )}
                </div>
                {item.result?.images && item.result.images.length > 0 && (
                  <div className="flex gap-2 mt-2">
                    {item.result.images.slice(0, 3).map((image, imgIndex) => (
                      <img
                        key={imgIndex}
                        src={image.url}
                        alt={`History ${index + 1} - Image ${imgIndex + 1}`}
                        className="w-12 h-12 object-cover rounded cursor-pointer hover:opacity-80 transition-opacity"
                        onClick={() => handleImagePreview(image.url, `History ${index + 1} - Image ${imgIndex + 1}`)}
                      />
                    ))}
                    {item.result.images.length > 3 && (
                      <div className="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded flex items-center justify-center text-xs text-gray-600 dark:text-gray-400">
                        +{item.result.images.length - 3}
                      </div>
                    )}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 图片预览模态框 */}
      {previewImage && (
        <ImagePreviewModal
          isOpen={!!previewImage}
          onClose={() => setPreviewImage(null)}
          imageUrl={previewImage.url}
          imageAlt={previewImage.alt}
          onDownload={() => handleImageDownload(previewImage.url)}
        />
      )}
    </div>
  );
}
